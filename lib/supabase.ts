import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ""
const supabaseKey =
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY || ""

export const supabase = supabaseUrl && supabaseKey ? createClient(supabaseUrl, supabaseKey) : null

export const isSupabaseConfigured = () => Boolean(supabaseUrl && supabaseKey)

// Database types
export interface Blog {
  id: string
  title: string
  slug: string
  content: string
  excerpt?: string
  author: string
  published_date: string
  tags: string[]
  featured_image?: string
  meta_title?: string
  meta_description?: string
  created_at: string
  updated_at: string
}

export interface Attorney {
  source_id: string
  title: string
  price?: number
  category_name?: string
  address: string
  neighborhood?: string
  street?: string
  city: string
  postal_code?: string
  state: string
  country_code?: string
  website?: string
  phone?: string
  phone_unformatted?: string
  claim_this_business?: boolean
  lat: number
  lng: number
  total_score: number
  permanently_closed?: boolean
  temporarily_closed?: boolean
  place_id?: string
  categories?: any
  fid?: string
  cid?: string
  reviews_count: number
  images_count?: number
  image_categories?: any
  scraped_at?: string
  google_food_url?: string
  hotel_ads?: any
  opening_hours?: any
  people_also_search?: any
  places_tags?: any
  reviews_tags?: any
  additional_info?: any
  gas_prices?: any
  search_page_url?: string
  search_string?: string
  language?: string
  rank?: number
  is_advertisement?: boolean
  image_url?: string
  kgmid?: string
  url: string
  processed_reviews?: {
    snippets?: Array<{
      id: string
      text: string
      keywords: string[]
      sentiment: string
    }>
    aggregates?: {
      total_reviews: number
      average_rating: number
      sentiment_distribution: {
        positive: number
        negative: number
        neutral: number
      }
    }
  }
  seo_snippets?: Array<{
    id: string
    text: string
    keywords: string[]
    sentiment: string
  }>
  service_keywords?: string[]
  faq_items?: Array<{
    question: string
    answer: string
    source: string
  }>
  review_stats?: any
  reviews_processed?: boolean
  last_reviews_update?: string
}

const mockAttorneys: Attorney[] = [
  {
    source_id: "john-smith-law",
    title: "John Smith Law Firm",
    address: "123 Main St, Los Angeles, CA 90210",
    city: "Los Angeles",
    state: "CA",
    country_code: "US",
    phone: "******-123-4567",
    website: "https://johnsmithlaw.com",
    lat: 34.0522,
    lng: -118.2437,
    total_score: 4.8,
    reviews_count: 127,
    url: "https://johnsmithlaw.com",
    reviews_processed: true,
    service_keywords: ["Car Accidents", "Personal Injury", "Insurance Claims"],
    processed_reviews: {
      aggregates: {
        total_reviews: 127,
        average_rating: 4.8,
        sentiment_distribution: {
          positive: 85,
          negative: 10,
          neutral: 5,
        },
      },
    },
  },
  {
    source_id: "sarah-johnson-attorney",
    title: "Sarah Johnson Attorney at Law",
    address: "456 Oak Ave, San Francisco, CA 94102",
    city: "San Francisco",
    state: "CA",
    country_code: "US",
    phone: "******-987-6543",
    website: "https://sarahjohnsonlaw.com",
    lat: 37.7749,
    lng: -122.4194,
    total_score: 4.9,
    reviews_count: 89,
    url: "https://sarahjohnsonlaw.com",
    reviews_processed: true,
    service_keywords: ["Auto Accidents", "Wrongful Death", "Medical Malpractice"],
    processed_reviews: {
      aggregates: {
        total_reviews: 89,
        average_rating: 4.9,
        sentiment_distribution: {
          positive: 90,
          negative: 5,
          neutral: 5,
        },
      },
    },
  },
  {
    source_id: "martinez-legal-group",
    title: "Martinez Legal Group",
    address: "789 Broadway, New York, NY 10003",
    city: "New York",
    state: "NY",
    country_code: "US",
    phone: "******-456-7890",
    website: "https://martinezlegal.com",
    lat: 40.7128,
    lng: -74.006,
    total_score: 4.7,
    reviews_count: 156,
    url: "https://martinezlegal.com",
    reviews_processed: true,
    service_keywords: ["Vehicle Accidents", "Slip and Fall", "Workers Compensation"],
    processed_reviews: {
      aggregates: {
        total_reviews: 156,
        average_rating: 4.7,
        sentiment_distribution: {
          positive: 80,
          negative: 15,
          neutral: 5,
        },
      },
    },
  },
]

// Database query functions
export async function getAttorneys(page = 1, limit = 20, city?: string, state?: string, searchTerm?: string) {
  if (!supabase) {
    console.warn("Supabase not configured, returning mock data")
    return {
      attorneys: mockAttorneys.slice((page - 1) * limit, page * limit),
      total: mockAttorneys.length,
      error: null,
    }
  }

  try {
    let query = supabase
      .from("findcaraccidentattorneys-clean")
      .select("*", { count: "exact" })
      .eq("reviews_processed", true)
      .order("total_score", { ascending: false })
      .order("reviews_count", { ascending: false })

    if (city) {
      query = query.ilike("city", `%${city}%`)
    }

    if (state) {
      query = query.ilike("state", `%${state}%`)
    }

    if (searchTerm) {
      query = query.or(`title.ilike.%${searchTerm}%,city.ilike.%${searchTerm}%,state.ilike.%${searchTerm}%,service_keywords.cs.{${searchTerm}}`)
    }

    const from = (page - 1) * limit
    const to = from + limit - 1

    const { data, error, count } = await query.range(from, to)

    if (error) {
      console.error("Error fetching attorneys:", error)
      return { attorneys: [], total: 0, error: error.message }
    }

    return {
      attorneys: data as Attorney[],
      total: count || 0,
      error: null,
    }
  } catch (error) {
    console.error("Unexpected error in getAttorneys:", error)
    return { attorneys: [], total: 0, error: "Unexpected error" }
  }
}

export async function getAttorneyBySlug(slug: string) {
  if (!supabase) {
    console.warn("Supabase not configured, returning mock data")
    // For mock data, try to find by generated slug or fallback to source_id
    const { generateAttorneySlug } = await import('./utils/seo')
    const attorney = mockAttorneys.find((a) =>
      generateAttorneySlug(a) === slug || a.source_id === slug
    )
    return { attorney: attorney || null, error: attorney ? null : "Attorney not found" }
  }

  // First try to find by generated slug (comparing against title)
  const { data: attorneys, error: searchError } = await supabase
    .from("findcaraccidentattorneys-clean")
    .select("*")
    .eq("reviews_processed", true)

  if (searchError) {
    console.error("Error fetching attorneys for slug search:", searchError)
    return { attorney: null, error: searchError.message }
  }

  // Find attorney by matching generated slug
  const { generateAttorneySlug } = await import('./utils/seo')
  const attorney = attorneys?.find((a) => generateAttorneySlug(a) === slug)

  if (attorney) {
    return { attorney: attorney as Attorney, error: null }
  }

  // Fallback: try to find by source_id for backward compatibility
  const { data, error } = await supabase
    .from("findcaraccidentattorneys-clean")
    .select("*")
    .eq("source_id", slug)
    .eq("reviews_processed", true)
    .single()

  if (error) {
    console.error("Error fetching attorney by source_id:", error)
    return { attorney: null, error: error.message }
  }

  return { attorney: data as Attorney, error: null }
}

export async function getStates() {
  if (!supabase) {
    console.warn("Supabase not configured, returning mock data")
    const uniqueStates = [...new Set(mockAttorneys.map((item) => item.state))].sort()
    return { states: uniqueStates, error: null }
  }

  const { data, error } = await supabase
    .from("findcaraccidentattorneys-clean")
    .select("state")
    .eq("reviews_processed", true)
    .not("state", "is", null)

  if (error) {
    console.error("Error fetching states:", error)
    return { states: [], error: error.message }
  }

  const uniqueStates = [...new Set(data.map((item) => item.state))].sort()
  return { states: uniqueStates, error: null }
}

export async function getCitiesByState(state: string) {
  if (!supabase) {
    console.warn("Supabase not configured, returning mock data")
    const cities = mockAttorneys
      .filter((item) => item.state.toLowerCase().includes(state.toLowerCase()))
      .map((item) => item.city)
    const uniqueCities = [...new Set(cities)].sort()
    return { cities: uniqueCities, error: null }
  }

  const { data, error } = await supabase
    .from("findcaraccidentattorneys-clean")
    .select("city")
    .eq("reviews_processed", true)
    .ilike("state", `%${state}%`)
    .not("city", "is", null)

  if (error) {
    console.error("Error fetching cities:", error)
    return { cities: [], error: error.message }
  }

  const uniqueCities = [...new Set(data.map((item) => item.city))].sort()
  return { cities: uniqueCities, error: null }
}

export async function getAttorneysByLocation(state: string, city?: string) {
  if (!supabase) {
    console.warn("Supabase not configured, returning mock data")
    let filtered = mockAttorneys.filter((item) => item.state.toLowerCase().includes(state.toLowerCase()))

    if (city) {
      filtered = filtered.filter((item) => item.city.toLowerCase().includes(city.toLowerCase()))
    }

    return { attorneys: filtered, error: null }
  }

  let query = supabase
    .from("findcaraccidentattorneys-clean")
    .select("*")
    .eq("reviews_processed", true)
    .ilike("state", `%${state}%`)
    .order("total_score", { ascending: false })
    .order("reviews_count", { ascending: false })

  if (city) {
    query = query.ilike("city", `%${city}%`)
  }

  const { data, error } = await query

  if (error) {
    console.error("Error fetching attorneys by location:", error)
    return { attorneys: [], error: error.message }
  }

  return { attorneys: data as Attorney[], error: null }
}

// Blog-related functions
export async function getBlogs(page = 1, limit = 20, searchTerm?: string, tag?: string) {
  if (!supabase) {
    console.warn("Supabase not configured, returning mock data")
    return {
      blogs: [],
      total: 0,
      error: null,
    }
  }

  const offset = (page - 1) * limit

  let query = supabase
    .from("findcaraccidentattorneys-blog")
    .select("*", { count: "exact" })
    .lte("published_date", new Date().toISOString().split('T')[0])
    .order("published_date", { ascending: false })

  if (searchTerm) {
    query = query.or(`title.ilike.%${searchTerm}%,excerpt.ilike.%${searchTerm}%`)
  }

  if (tag) {
    query = query.contains("tags", [tag])
  }

  const { data, error, count } = await query.range(offset, offset + limit - 1)

  if (error) {
    console.error("Error fetching blogs:", error)
    return { blogs: [], total: 0, error: error.message }
  }

  return { blogs: data as Blog[], total: count || 0, error: null }
}

export async function getBlogBySlug(slug: string) {
  if (!supabase) {
    console.warn("Supabase not configured, returning mock data")
    return { blog: null, error: "Blog not found" }
  }

  const { data, error } = await supabase
    .from("findcaraccidentattorneys-blog")
    .select("*")
    .eq("slug", slug)
    .lte("published_date", new Date().toISOString().split('T')[0])
    .single()

  if (error) {
    console.error("Error fetching blog by slug:", error)
    return { blog: null, error: error.message }
  }

  return { blog: data as Blog, error: null }
}

export async function getRelatedBlogs(currentBlogId: string, tags: string[], limit = 3) {
  if (!supabase) {
    console.warn("Supabase not configured, returning mock data")
    return { blogs: [], error: null }
  }

  const { data, error } = await supabase
    .from("findcaraccidentattorneys-blog")
    .select("*")
    .neq("id", currentBlogId)
    .overlaps("tags", tags)
    .lte("published_date", new Date().toISOString().split('T')[0])
    .order("published_date", { ascending: false })
    .limit(limit)

  if (error) {
    console.error("Error fetching related blogs:", error)
    return { blogs: [], error: error.message }
  }

  return { blogs: data as Blog[], error: null }
}

export async function getRelatedAttorneys(currentAttorneyId: string, city: string, limit = 3) {
  if (!supabase) {
    console.warn("Supabase not configured, returning mock data")
    // For mock data, return some sample attorneys from the same city
    const relatedAttorneys = mockAttorneys
      .filter(a => a.source_id !== currentAttorneyId && a.city.toLowerCase() === city.toLowerCase())
      .slice(0, limit)
    return { attorneys: relatedAttorneys, error: null }
  }

  const { data, error } = await supabase
    .from("findcaraccidentattorneys-clean")
    .select("*")
    .neq("source_id", currentAttorneyId)
    .ilike("city", `%${city}%`)
    .eq("reviews_processed", true)
    .order("total_score", { ascending: false })
    .limit(limit)

  if (error) {
    console.error("Error fetching related attorneys:", error)
    return { attorneys: [], error: error.message }
  }

  return { attorneys: data as Attorney[], error: null }
}

export async function getBlogTags() {
  if (!supabase) {
    console.warn("Supabase not configured, returning mock data")
    return { tags: ["car-accidents", "legal-advice", "personal-injury", "attorney-selection"], error: null }
  }

  const { data, error } = await supabase
    .from("findcaraccidentattorneys-blog")
    .select("tags")

  if (error) {
    console.error("Error fetching blog tags:", error)
    return { tags: [], error: error.message }
  }

  // Extract unique tags from all blog posts
  const allTags = data?.flatMap(blog => blog.tags || []) || []
  const uniqueTags = [...new Set(allTags)].sort()

  return { tags: uniqueTags, error: null }
}
