# Blog Management Scripts

This directory contains two powerful scripts for managing blog content and images for your directory website.

## 📋 Overview

1. **blog-creator.js** - Creates blog posts and inserts them into the database
2. **blog-image-generator.js** - Generates images for blog posts using Kie.ai API and stores them in Cloudflare R2

## 🚀 Setup Instructions

### Prerequisites

1. Install Node.js dependencies:
```bash
npm install @supabase/supabase-js node-fetch
```

2. Copy the script files from the repository (they're in .gitignore for security)

### Configuration

#### For blog-creator.js:

1. Update `SUPABASE_CONFIG` with your project details:
   - `url`: Your Supabase project URL
   - `key`: Your Supabase anon key
   - `tableName`: Your blog table name

2. Customize `BLOG_CONFIG`:
   - `keywords`: Array of relevant keywords for your niche
   - `topics`: Array of blog post topics to generate
   - `numberOfArticles`: How many articles to create
   - `author`: Default author name
   - `tags`: Available tags for categorization

#### For blog-image-generator.js:

1. Update `SUPABASE_CONFIG` (same as above)

2. Set `KIE_AI_CONFIG`:
   - `apiKey`: Your Kie.ai API key (provided: 5c0cbed5f2b7ef0a1aea4fea91eefad3)

3. Configure `CLOUDFLARE_R2_CONFIG`:
   - `endpoint`: Your R2 endpoint URL
   - `bucketName`: Your R2 bucket name
   - Add your R2 access credentials

## 📝 Usage

### Creating Blog Posts

```bash
node blog-creator.js
```

This script will:
- Generate blog posts based on your configured topics and keywords
- Create SEO-optimized content with proper meta tags
- Insert posts into your Supabase blog table
- Set future publish dates for scheduled content

### Generating Blog Images

```bash
node blog-image-generator.js
```

This script will:
- Find blog posts without featured images
- Generate relevant images using Kie.ai API
- Upload images to Cloudflare R2
- Update blog posts with image URLs

## 🔧 Customization

### Adding New Keywords

Edit the `keywords` array in `BLOG_CONFIG`:

```javascript
keywords: [
  'your-keyword-1',
  'your-keyword-2',
  'your-keyword-3'
]
```

### Adding New Topics

Edit the `topics` array in `BLOG_CONFIG`:

```javascript
topics: [
  'Your Custom Topic 1',
  'Your Custom Topic 2',
  'Your Custom Topic 3'
]
```

### Customizing Image Prompts

Edit the `IMAGE_PROMPTS` object in blog-image-generator.js:

```javascript
IMAGE_PROMPTS = {
  'your-keyword': 'Your custom image generation prompt',
  'another-keyword': 'Another custom prompt'
}
```

## 🔒 Security Notes

- These files contain API keys and are added to .gitignore
- Never commit these files to version control
- Keep your API keys secure and rotate them regularly
- Use environment variables in production

## 📊 Features

### Blog Creator Features:
- ✅ SEO-optimized content generation
- ✅ Automatic slug generation
- ✅ Meta title and description creation
- ✅ Random tag assignment
- ✅ Future date scheduling
- ✅ Keyword integration

### Image Generator Features:
- ✅ Intelligent prompt generation based on content
- ✅ Kie.ai API integration
- ✅ Cloudflare R2 storage
- ✅ Automatic database updates
- ✅ Rate limiting protection
- ✅ Error handling and retry logic

## 🛠 Troubleshooting

### Common Issues:

1. **Supabase Connection Error**
   - Verify your URL and API key
   - Check table name spelling
   - Ensure proper permissions

2. **Kie.ai API Error**
   - Verify API key is correct
   - Check rate limits
   - Ensure sufficient credits

3. **R2 Upload Error**
   - Verify R2 credentials
   - Check bucket permissions
   - Ensure endpoint URL is correct

### Error Messages:

- `❌ Error inserting blog posts` - Check Supabase configuration
- `❌ Error generating image` - Check Kie.ai API key and credits
- `❌ Error uploading to R2` - Check R2 configuration and credentials

## 📈 Best Practices

1. **Content Quality**: Review generated content before publishing
2. **SEO Optimization**: Ensure keywords are naturally integrated
3. **Image Relevance**: Verify generated images match content
4. **Publishing Schedule**: Space out publish dates appropriately
5. **Tag Management**: Use consistent tagging strategy

## 🔄 Workflow

Recommended workflow for content creation:

1. Run `blog-creator.js` to generate blog posts
2. Review and edit content as needed in your database
3. Run `blog-image-generator.js` to add featured images
4. Verify images and content quality
5. Publish according to your schedule

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify all configuration settings
3. Check API documentation for Kie.ai and Supabase
4. Review error messages for specific guidance
